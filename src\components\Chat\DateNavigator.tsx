import React, { useState, useEffect } from 'react';
import { Calendar, ChevronDown, ArrowRight } from 'lucide-react';
import { ChatMessage } from '../../types/chat';

interface DateNavigatorProps {
  messages: ChatMessage[];
  onDateSelect: (year: number, month: number) => void;
}

interface DateInfo {
  year: number;
  month: number;
  monthName: string;
  messageCount: number;
}

export const DateNavigator: React.FC<DateNavigatorProps> = ({ 
  messages, 
  onDateSelect 
}) => {
  const [availableDates, setAvailableDates] = useState<DateInfo[]>([]);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', '<PERSON><PERSON>',
    '<PERSON>', 'Agosto', 'Septiembre', 'Octubre', 'Noviem<PERSON>', 'Diciembre'
  ];

  useEffect(() => {
    if (messages.length === 0) return;

    // Extraer todas las fechas únicas del chat
    const dateMap = new Map<string, number>();
    
    messages.forEach(message => {
      const dateMatch = message.timestamp.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})/);
      if (dateMatch) {
        const [, day, month, year] = dateMatch;
        const fullYear = year.length === 2 ? parseInt(`20${year}`) : parseInt(year);
        const monthNum = parseInt(month);
        
        const key = `${fullYear}-${monthNum}`;
        dateMap.set(key, (dateMap.get(key) || 0) + 1);
      }
    });

    // Convertir a array y ordenar por fecha
    const dates: DateInfo[] = Array.from(dateMap.entries())
      .map(([key, count]) => {
        const [year, month] = key.split('-').map(Number);
        return {
          year,
          month,
          monthName: monthNames[month - 1],
          messageCount: count
        };
      })
      .sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });

    setAvailableDates(dates);

    // Establecer la fecha más antigua como predeterminada
    if (dates.length > 0) {
      const oldest = dates[0];
      setSelectedYear(oldest.year);
      setSelectedMonth(oldest.month);
    }
  }, [messages]);

  const handleNavigate = () => {
    if (selectedYear && selectedMonth) {
      onDateSelect(selectedYear, selectedMonth);
      setIsOpen(false);
    }
  };

  const getAvailableYears = () => {
    const years = [...new Set(availableDates.map(d => d.year))];
    return years.sort((a, b) => a - b);
  };

  const getAvailableMonths = (year: number) => {
    return availableDates
      .filter(d => d.year === year)
      .sort((a, b) => a.month - b.month);
  };

  const getSelectedDateInfo = () => {
    if (!selectedYear || !selectedMonth) return null;
    return availableDates.find(d => d.year === selectedYear && d.month === selectedMonth);
  };

  if (availableDates.length === 0) return null;

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        title="Buscar por fecha"
      >
        <Calendar className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">
          {selectedYear && selectedMonth 
            ? `${monthNames[selectedMonth - 1]} ${selectedYear}`
            : 'Seleccionar fecha'
          }
        </span>
        <ChevronDown className={`w-4 h-4 text-gray-600 transition-transform ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50 min-w-80">
          <div className="p-4 space-y-4">
            <h3 className="font-semibold text-gray-800 flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Navegar por fecha
            </h3>

            {/* Selector de año */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Año
              </label>
              <select
                value={selectedYear || ''}
                onChange={(e) => {
                  const year = parseInt(e.target.value);
                  setSelectedYear(year);
                  // Resetear mes si no está disponible en el nuevo año
                  const monthsInYear = getAvailableMonths(year);
                  if (selectedMonth && !monthsInYear.find(m => m.month === selectedMonth)) {
                    setSelectedMonth(monthsInYear[0]?.month || null);
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-whatsapp-green"
              >
                <option value="">Seleccionar año</option>
                {getAvailableYears().map(year => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>

            {/* Selector de mes */}
            {selectedYear && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mes
                </label>
                <select
                  value={selectedMonth || ''}
                  onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-whatsapp-green"
                >
                  <option value="">Seleccionar mes</option>
                  {getAvailableMonths(selectedYear).map(dateInfo => (
                    <option key={dateInfo.month} value={dateInfo.month}>
                      {dateInfo.monthName} ({dateInfo.messageCount} mensajes)
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Información de la fecha seleccionada */}
            {getSelectedDateInfo() && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="text-sm text-blue-800">
                  <strong>{getSelectedDateInfo()?.messageCount} mensajes</strong> en{' '}
                  {getSelectedDateInfo()?.monthName} {selectedYear}
                </div>
              </div>
            )}

            {/* Botones */}
            <div className="flex gap-2 pt-2">
              <button
                onClick={handleNavigate}
                disabled={!selectedYear || !selectedMonth}
                className="flex items-center gap-2 px-4 py-2 bg-whatsapp-green text-white rounded-md hover:bg-whatsapp-green-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ArrowRight className="w-4 h-4" />
                Ir a fecha
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
