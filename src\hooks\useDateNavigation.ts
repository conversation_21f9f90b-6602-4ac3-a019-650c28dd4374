import { useState, useCallback, useRef } from 'react';
import { ChatMessage } from '../types/chat';

interface UseDateNavigationReturn {
  scrollToDate: (year: number, month: number) => void;
  registerMessageRef: (messageId: string, element: HTMLElement | null) => void;
  highlightedMessageId: string | null;
  clearHighlight: () => void;
}

export const useDateNavigation = (messages: ChatMessage[]): UseDateNavigationReturn => {
  const [highlightedMessageId, setHighlightedMessageId] = useState<string | null>(null);
  const messageRefs = useRef<Map<string, HTMLElement>>(new Map());
  const highlightTimeoutRef = useRef<NodeJS.Timeout>();

  const registerMessageRef = useCallback((messageId: string, element: HTMLElement | null) => {
    if (element) {
      messageRefs.current.set(messageId, element);
    } else {
      messageRefs.current.delete(messageId);
    }
  }, []);

  const scrollToDate = useCallback((year: number, month: number) => {
    // Buscar el primer mensaje del mes/año especificado
    const targetMessage = messages.find(message => {
      const dateMatch = message.timestamp.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})/);
      if (dateMatch) {
        const [, , msgMonth, msgYear] = dateMatch;
        const fullYear = msgYear.length === 2 ? parseInt(`20${msgYear}`) : parseInt(msgYear);
        const monthNum = parseInt(msgMonth);
        
        return fullYear === year && monthNum === month;
      }
      return false;
    });

    if (targetMessage) {
      const element = messageRefs.current.get(targetMessage.id);
      if (element) {
        // Scroll suave al elemento
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });

        // Highlight temporal del mensaje
        setHighlightedMessageId(targetMessage.id);
        
        // Limpiar highlight después de 3 segundos
        if (highlightTimeoutRef.current) {
          clearTimeout(highlightTimeoutRef.current);
        }
        highlightTimeoutRef.current = setTimeout(() => {
          setHighlightedMessageId(null);
        }, 3000);
      }
    }
  }, [messages]);

  const clearHighlight = useCallback(() => {
    setHighlightedMessageId(null);
    if (highlightTimeoutRef.current) {
      clearTimeout(highlightTimeoutRef.current);
    }
  }, []);

  return {
    scrollToDate,
    registerMessageRef,
    highlightedMessageId,
    clearHighlight
  };
};
