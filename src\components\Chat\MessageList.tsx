import React, { useEffect, useRef } from 'react';
import { ChatMessage } from '../../types/chat';
import { MessageBubble } from './MessageBubble';
import { Calendar } from 'lucide-react';

interface MessageListProps {
  messages: ChatMessage[];
  mediaFiles: FileList | null;
  currentUser?: string;
  onRegisterMessageRef?: (messageId: string, element: HTMLElement | null) => void;
  highlightedMessageId?: string | null;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  mediaFiles,
  currentUser = 'Tú',
  onRegisterMessageRef,
  highlightedMessageId
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const formatDateSeparator = (timestamp: string): string => {
    try {
      const dateMatch = timestamp.match(/^(\d{1,2}\/\d{1,2}\/\d{2,4})/);
      if (dateMatch) {
        const [, date] = dateMatch;
        const [day, month, year] = date.split('/');
        const fullYear = year.length === 2 ? `20${year}` : year;

        const dateObj = new Date(parseInt(fullYear), parseInt(month) - 1, parseInt(day));
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (dateObj.toDateString() === today.toDateString()) {
          return 'Hoy';
        } else if (dateObj.toDateString() === yesterday.toDateString()) {
          return 'Ayer';
        } else {
          return dateObj.toLocaleDateString('es-ES', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        }
      }
      return timestamp;
    } catch {
      return timestamp;
    }
  };

  const shouldShowDateSeparator = (currentMessage: ChatMessage, previousMessage?: ChatMessage): boolean => {
    if (!previousMessage) return true;

    const currentDate = currentMessage.timestamp.match(/^(\d{1,2}\/\d{1,2}\/\d{2,4})/)?.[1];
    const previousDate = previousMessage.timestamp.match(/^(\d{1,2}\/\d{1,2}\/\d{2,4})/)?.[1];

    return currentDate !== previousDate;
  };

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-lg mb-2">No hay mensajes para mostrar</p>
          <p className="text-sm">Carga un archivo de chat para comenzar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-2">
      {messages.map((message, index) => {
        const isOwn = message.sender === currentUser ||
                     message.sender.toLowerCase().includes('tú') ||
                     message.sender.toLowerCase().includes('you');

        const previousMessage = index > 0 ? messages[index - 1] : undefined;
        const showDateSeparator = shouldShowDateSeparator(message, previousMessage);

        return (
          <React.Fragment key={message.id}>
            {showDateSeparator && (
              <div className="flex justify-center my-4">
                <div className="bg-gray-200 text-gray-600 px-3 py-1 rounded-full text-xs font-medium flex items-center gap-2">
                  <Calendar className="w-3 h-3" />
                  {formatDateSeparator(message.timestamp)}
                </div>
              </div>
            )}
            <MessageBubble
              message={message}
              isOwn={isOwn}
              mediaFiles={mediaFiles}
              onRegisterRef={onRegisterMessageRef}
              isHighlighted={highlightedMessageId === message.id}
            />
          </React.Fragment>
        );
      })}
      <div ref={messagesEndRef} />
    </div>
  );
};
