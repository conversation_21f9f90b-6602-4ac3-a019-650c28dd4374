import React from 'react';
import { ChatData } from '../../types/chat';
import { MessageList } from './MessageList';
import { DateNavigator } from './DateNavigator';
import { useDateNavigation } from '../../hooks/useDateNavigation';
import { MessageCircle, Image, Music, Video, Users } from 'lucide-react';

interface ChatContainerProps {
  chatData: ChatData;
  mediaFiles: FileList | null;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  chatData,
  mediaFiles
}) => {
  const { scrollToDate, registerMessageRef, highlightedMessageId } = useDateNavigation(chatData.messages);
  const getStats = () => {
    const totalMessages = chatData.messages.length;
    const imageCount = chatData.messages.filter(m => m.type === 'image' || m.type === 'sticker').length;
    const audioCount = chatData.messages.filter(m => m.type === 'audio').length;
    const videoCount = chatData.messages.filter(m => m.type === 'video').length;
    const textCount = chatData.messages.filter(m => m.type === 'text').length;
    
    const participants = new Set(chatData.messages.map(m => m.sender)).size;
    
    return {
      totalMessages,
      imageCount,
      audioCount,
      videoCount,
      textCount,
      participants
    };
  };

  const stats = getStats();

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Chat Header */}
      <div className="bg-whatsapp-green text-white p-4 shadow-md">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{chatData.title}</h2>
            <p className="text-sm text-green-100">
              {stats.totalMessages} mensajes • {stats.participants} participantes
            </p>
          </div>

          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <MessageCircle className="w-4 h-4" />
              <span>{stats.textCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <Image className="w-4 h-4" />
              <span>{stats.imageCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <Music className="w-4 h-4" />
              <span>{stats.audioCount}</span>
            </div>
            <div className="flex items-center gap-1">
              <Video className="w-4 h-4" />
              <span>{stats.videoCount}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Date Navigator */}
      <div className="bg-gray-50 border-b border-gray-200 p-3">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Navegar por fecha:
          </div>
          <DateNavigator
            messages={chatData.messages}
            onDateSelect={scrollToDate}
          />
        </div>
      </div>

      {/* Messages */}
      <MessageList
        messages={chatData.messages}
        mediaFiles={mediaFiles}
        onRegisterMessageRef={registerMessageRef}
        highlightedMessageId={highlightedMessageId}
      />
      
      {/* Footer info */}
      <div className="bg-white border-t p-2 text-center text-xs text-gray-500">
        Chat exportado de WhatsApp • {mediaFiles ? `${mediaFiles.length} archivos multimedia` : 'Sin archivos multimedia'}
      </div>
    </div>
  );
};
