# Chat2What - Visualizador de Chats WhatsApp

Una aplicación web moderna para visualizar chats exportados de WhatsApp con soporte completo para multimedia y thumbnails optimizados.

## 🚀 Características

- ✅ **Interfaz tipo WhatsApp** - Diseño verde característico con burbujas de chat
- ✅ **Parser de chats** - Lee archivos .txt exportados de WhatsApp
- ✅ **Thumbnails optimizados** - Previsualización de baja calidad para mantener fluidez
- ✅ **Carga lazy de multimedia** - Solo carga archivos cuando haces click
- ✅ **Soporte completo de archivos:**
  - 🖼️ Imágenes (.jpg, .png, .webp) con modal de vista ampliada
  - 🎵 Audios (.opus) con reproductor personalizado
  - 🎬 Videos (.mp4) con controles completos y thumbnails
  - 🎭 Stickers (.webp)
- ✅ **Separadores de fecha** - Organización por días como WhatsApp
- ✅ **Fechas y horas completas** - Muestra timestamp completo de cada mensaje

## 🛠️ Tecnologías

- **React + TypeScript** - Framework principal
- **Vite** - Build tool rápido con hot reload
- **Tailwind CSS** - Estilos modernos y responsive
- **Lucide React** - Iconos
- **Canvas API** - Generación de thumbnails optimizados
- **File API** - Manejo de archivos locales
- **HTML5 Audio/Video** - Reproductores multimedia

## 📦 Instalación

```bash
# Instalar dependencias
npm install

# Iniciar servidor de desarrollo
npm run dev

# Construir para producción
npm run build
```

## 🎯 Cómo usar

1. **Exporta tu chat de WhatsApp:**
   - Abre el chat en WhatsApp
   - Ve a Configuración > Exportar chat
   - Selecciona "Incluir multimedia"

2. **Carga el archivo .txt:**
   - Usa el panel lateral para seleccionar el archivo .txt del chat

3. **Selecciona la carpeta multimedia:**
   - Carga la carpeta que contiene las imágenes, audios y videos

4. **¡Disfruta navegando por tu chat!**

## 📁 Estructura del proyecto

```
src/
├── components/
│   ├── Chat/           # Componentes del chat
│   ├── Menu/           # Selectores de archivos
│   ├── Multimedia/     # Reproductores y modales
│   └── Layout/         # Header y sidebar
├── hooks/              # Hooks personalizados
├── utils/              # Utilidades y parsers
├── types/              # Tipos TypeScript
└── styles/             # Estilos CSS
```

## 🎨 Características de Performance

### Thumbnails Optimizados
- **Imágenes**: Redimensionadas a máximo 150px de ancho con calidad 30%
- **Videos**: Thumbnails generados del primer segundo con calidad 40%
- **Cache inteligente**: Los thumbnails se cargan una sola vez y se mantienen en memoria
- **Carga progresiva**: Thumbnail primero, imagen completa al hacer click

### Gestión de Memoria
- URLs de objeto se liberan automáticamente
- Cache separado para thumbnails y archivos completos
- Limpieza automática al cambiar de chat

## 📝 Formato de Chat Soportado

El formato esperado es el estándar de WhatsApp:

```
3/05/24 12:56 p. m. - Diana Muñeca: Cuánto te testo
3/05/24 12:56 p. m. - Diana Muñeca: ‎PTT-20240503-WA0037.opus (archivo adjunto)
3/05/24 7:16 p. m. - Diana Muñeca: ‎IMG-20240503-WA0119.jpg (archivo adjunto)
2/09/24 4:28 p. m. - Grupo JDK: ‎VID-20240902-WA0089.mp4 (archivo adjunto)
```

## 🔧 Configuración

### Calidad de Thumbnails
Puedes ajustar la calidad en `src/utils/mediaUtils.ts`:

```typescript
// Para imágenes
generateImageThumbnail(file, maxWidth: 150, quality: 0.3)

// Para videos  
canvas.toBlob(callback, 'image/jpeg', 0.4)
```

## 🚀 Próximas características

- [ ] Búsqueda en mensajes
- [ ] Exportar chat a PDF
- [ ] Modo oscuro
- [ ] Estadísticas avanzadas
- [ ] Soporte para más formatos de archivo

## 📄 Licencia

MIT License - Siéntete libre de usar y modificar este proyecto.

---

**Desarrollado con ❤️ para visualizar tus recuerdos de WhatsApp**
