import React, { useState } from 'react';
import { ChatMessage } from '../../types/chat';
import { MediaThumbnail } from '../Multimedia/MediaThumbnail';
import { AudioPlayer } from '../Multimedia/AudioPlayer';
import { VideoPlayer } from '../Multimedia/VideoPlayer';
import { ImageModal } from '../Multimedia/ImageModal';
import { useMediaCache } from '../../hooks/useMediaCache';
import { Play } from 'lucide-react';

interface MessageBubbleProps {
  message: ChatMessage;
  isOwn: boolean;
  mediaFiles: FileList | null;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  mediaFiles
}) => {
  const [mediaUrl, setMediaUrl] = useState<string | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [loadingMedia, setLoadingMedia] = useState(false);
  const [loadingThumbnail, setLoadingThumbnail] = useState(false);
  const { getMediaURL, getThumbnailURL } = useMediaCache(mediaFiles);

  const formatDateTime = (timestamp: string): { date: string; time: string } => {
    // Formato original: "3/05/24 12:56 p. m."
    try {
      const regex = /^(\d{1,2}\/\d{1,2}\/\d{2,4})\s+(\d{1,2}:\d{2})\s+([ap])\.\s+m\.$/;
      const match = timestamp.match(regex);

      if (match) {
        const [, date, time, period] = match;
        return {
          date: date,
          time: `${time} ${period.toUpperCase()}M`
        };
      }

      return {
        date: timestamp.split(' ')[0] || '',
        time: timestamp
      };
    } catch {
      return {
        date: '',
        time: timestamp
      };
    }
  };

  const loadThumbnail = async () => {
    if (!message.fileName || !mediaFiles || thumbnailUrl) return;

    setLoadingThumbnail(true);
    try {
      const url = await getThumbnailURL(message.fileName);
      setThumbnailUrl(url);
    } catch (error) {
      console.error('Error cargando thumbnail:', error);
    } finally {
      setLoadingThumbnail(false);
    }
  };

  const handleMediaClick = async () => {
    if (!message.fileName || !mediaFiles) return;

    setLoadingMedia(true);
    try {
      const url = await getMediaURL(message.fileName);
      setMediaUrl(url);

      if (message.type === 'image' || message.type === 'sticker') {
        setShowImageModal(true);
      }
    } catch (error) {
      console.error('Error cargando media:', error);
      alert('No se pudo cargar el archivo multimedia');
    } finally {
      setLoadingMedia(false);
    }
  };

  // Cargar thumbnail automáticamente para imágenes y videos
  React.useEffect(() => {
    if ((message.type === 'image' || message.type === 'video' || message.type === 'sticker') &&
        message.fileName && mediaFiles && !thumbnailUrl && !loadingThumbnail) {
      loadThumbnail();
    }
  }, [message.fileName, message.type, mediaFiles, thumbnailUrl, loadingThumbnail]);

  const renderMediaContent = () => {
    if (!message.fileName) return null;

    // Si ya tenemos la URL del media, mostrar el reproductor correspondiente
    if (mediaUrl) {
      switch (message.type) {
        case 'audio':
          return (
            <AudioPlayer
              audioUrl={mediaUrl}
              fileName={message.fileName}
            />
          );
        case 'video':
          return (
            <VideoPlayer
              videoUrl={mediaUrl}
              fileName={message.fileName}
            />
          );
        case 'image':
        case 'sticker':
          return (
            <div className="cursor-pointer" onClick={() => setShowImageModal(true)}>
              <img
                src={mediaUrl}
                alt={message.fileName}
                className="max-w-xs max-h-64 rounded-lg object-cover"
                onError={() => console.error('Error cargando imagen:', message.fileName)}
              />
            </div>
          );
        default:
          return null;
      }
    }

    // Si no tenemos la URL completa, mostrar thumbnail o botón de carga
    if ((message.type === 'image' || message.type === 'sticker') && thumbnailUrl) {
      return (
        <div className="relative cursor-pointer" onClick={handleMediaClick}>
          <img
            src={thumbnailUrl}
            alt={message.fileName}
            className="max-w-xs max-h-48 rounded-lg object-cover"
            style={{ filter: loadingMedia ? 'blur(2px)' : 'none' }}
          />
          {loadingMedia && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
            </div>
          )}
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
            Click para ampliar
          </div>
        </div>
      );
    }

    if (message.type === 'video' && thumbnailUrl) {
      return (
        <div className="relative cursor-pointer" onClick={handleMediaClick}>
          <img
            src={thumbnailUrl}
            alt={message.fileName}
            className="max-w-xs max-h-48 rounded-lg object-cover"
          />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-12 h-12 bg-black bg-opacity-60 rounded-full flex items-center justify-center">
              <Play className="w-6 h-6 text-white ml-1" />
            </div>
          </div>
          {loadingMedia && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent"></div>
            </div>
          )}
        </div>
      );
    }

    // Para otros tipos o mientras carga el thumbnail
    return (
      <MediaThumbnail
        fileName={message.fileName}
        mediaType={message.type as 'image' | 'audio' | 'video' | 'sticker'}
        onClick={handleMediaClick}
        loading={loadingMedia || loadingThumbnail}
      />
    );
  };

  return (
    <>
      <div className={`flex mb-4 ${isOwn ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
          isOwn 
            ? 'bg-whatsapp-green text-white' 
            : 'bg-white text-gray-800 shadow-md'
        }`}>
          {/* Sender name (only for received messages) */}
          {!isOwn && (
            <div className="text-sm font-semibold text-whatsapp-green-dark mb-1">
              {message.sender}
            </div>
          )}
          
          {/* Message content */}
          <div className="mb-2">
            {message.type === 'text' ? (
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
            ) : (
              renderMediaContent()
            )}
          </div>
          
          {/* Timestamp */}
          <div className={`text-xs ${
            isOwn ? 'text-green-100' : 'text-gray-500'
          } text-right`}>
            <div className="opacity-75 mb-1">
              {formatDateTime(message.timestamp).date}
            </div>
            <div className="font-medium">
              {formatDateTime(message.timestamp).time}
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && mediaUrl && (
        <ImageModal
          isOpen={showImageModal}
          imageUrl={mediaUrl}
          fileName={message.fileName || ''}
          onClose={() => setShowImageModal(false)}
        />
      )}
    </>
  );
};
