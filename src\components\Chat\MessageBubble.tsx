import React, { useState } from 'react';
import { ChatMessage } from '../../types/chat';
import { MediaThumbnail } from '../Multimedia/MediaThumbnail';
import { AudioPlayer } from '../Multimedia/AudioPlayer';
import { VideoPlayer } from '../Multimedia/VideoPlayer';
import { ImageModal } from '../Multimedia/ImageModal';
import { useMediaCache } from '../../hooks/useMediaCache';

interface MessageBubbleProps {
  message: ChatMessage;
  isOwn: boolean;
  mediaFiles: FileList | null;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ 
  message, 
  isOwn, 
  mediaFiles 
}) => {
  const [mediaUrl, setMediaUrl] = useState<string | null>(null);
  const [showImageModal, setShowImageModal] = useState(false);
  const [loadingMedia, setLoadingMedia] = useState(false);
  const { getMediaURL } = useMediaCache(mediaFiles);

  const formatDateTime = (timestamp: string): { date: string; time: string } => {
    // Formato original: "3/05/24 12:56 p. m."
    try {
      const regex = /^(\d{1,2}\/\d{1,2}\/\d{2,4})\s+(\d{1,2}:\d{2})\s+([ap])\.\s+m\.$/;
      const match = timestamp.match(regex);

      if (match) {
        const [, date, time, period] = match;
        return {
          date: date,
          time: `${time} ${period.toUpperCase()}M`
        };
      }

      return {
        date: timestamp.split(' ')[0] || '',
        time: timestamp
      };
    } catch {
      return {
        date: '',
        time: timestamp
      };
    }
  };

  const handleMediaClick = async () => {
    if (!message.fileName || !mediaFiles) return;

    setLoadingMedia(true);
    try {
      const url = await getMediaURL(message.fileName);
      setMediaUrl(url);
      
      if (message.type === 'image' || message.type === 'sticker') {
        setShowImageModal(true);
      }
    } catch (error) {
      console.error('Error cargando media:', error);
      alert('No se pudo cargar el archivo multimedia');
    } finally {
      setLoadingMedia(false);
    }
  };

  const renderMediaContent = () => {
    if (!message.fileName) return null;

    // Si ya tenemos la URL del media, mostrar el reproductor correspondiente
    if (mediaUrl) {
      switch (message.type) {
        case 'audio':
          return (
            <AudioPlayer
              audioUrl={mediaUrl}
              fileName={message.fileName}
            />
          );
        case 'video':
          return (
            <VideoPlayer
              videoUrl={mediaUrl}
              fileName={message.fileName}
            />
          );
        case 'image':
        case 'sticker':
          return (
            <div className="cursor-pointer" onClick={() => setShowImageModal(true)}>
              <img
                src={mediaUrl}
                alt={message.fileName}
                className="max-w-xs max-h-64 rounded-lg object-cover"
                onError={() => console.error('Error cargando imagen:', message.fileName)}
              />
            </div>
          );
        default:
          return null;
      }
    }

    // Si no tenemos la URL, mostrar thumbnail
    return (
      <MediaThumbnail
        fileName={message.fileName}
        mediaType={message.type as 'image' | 'audio' | 'video' | 'sticker'}
        onClick={handleMediaClick}
        loading={loadingMedia}
      />
    );
  };

  return (
    <>
      <div className={`flex mb-4 ${isOwn ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
          isOwn 
            ? 'bg-whatsapp-green text-white' 
            : 'bg-white text-gray-800 shadow-md'
        }`}>
          {/* Sender name (only for received messages) */}
          {!isOwn && (
            <div className="text-sm font-semibold text-whatsapp-green-dark mb-1">
              {message.sender}
            </div>
          )}
          
          {/* Message content */}
          <div className="mb-2">
            {message.type === 'text' ? (
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
            ) : (
              renderMediaContent()
            )}
          </div>
          
          {/* Timestamp */}
          <div className={`text-xs ${
            isOwn ? 'text-green-100' : 'text-gray-500'
          } text-right`}>
            <div className="opacity-75 mb-1">
              {formatDateTime(message.timestamp).date}
            </div>
            <div className="font-medium">
              {formatDateTime(message.timestamp).time}
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && mediaUrl && (
        <ImageModal
          isOpen={showImageModal}
          imageUrl={mediaUrl}
          fileName={message.fileName || ''}
          onClose={() => setShowImageModal(false)}
        />
      )}
    </>
  );
};
