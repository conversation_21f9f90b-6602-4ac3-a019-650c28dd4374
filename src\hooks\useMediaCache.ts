import { useState, useCallback, useEffect } from 'react';
import { mediaCache } from '../utils/mediaUtils';

interface UseMediaCacheReturn {
  getMediaURL: (fileName: string) => Promise<string>;
  getThumbnailURL: (fileName: string) => Promise<string>;
  clearCache: () => void;
  cacheSize: number;
  loadingFiles: Set<string>;
  loadingThumbnails: Set<string>;
}

export const useMediaCache = (mediaFiles: FileList | null): UseMediaCacheReturn => {
  const [cacheSize, setCacheSize] = useState(0);
  const [loadingFiles, setLoadingFiles] = useState<Set<string>>(new Set());
  const [loadingThumbnails, setLoadingThumbnails] = useState<Set<string>>(new Set());

  const getMediaURL = useCallback(async (fileName: string): Promise<string> => {
    if (!mediaFiles) {
      throw new Error('No hay archivos de media cargados');
    }

    setLoadingFiles(prev => new Set(prev).add(fileName));

    try {
      const url = await mediaCache.getMediaURL(fileName, mediaFiles);
      setCacheSize(mediaCache.getCacheSize());
      return url;
    } finally {
      setLoadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileName);
        return newSet;
      });
    }
  }, [mediaFiles]);

  const getThumbnailURL = useCallback(async (fileName: string): Promise<string> => {
    if (!mediaFiles) {
      throw new Error('No hay archivos de media cargados');
    }

    setLoadingThumbnails(prev => new Set(prev).add(fileName));

    try {
      const url = await mediaCache.getThumbnailURL(fileName, mediaFiles);
      setCacheSize(mediaCache.getCacheSize());
      return url;
    } finally {
      setLoadingThumbnails(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileName);
        return newSet;
      });
    }
  }, [mediaFiles]);

  const clearCache = useCallback(() => {
    mediaCache.clearCache();
    setCacheSize(0);
    setLoadingFiles(new Set());
    setLoadingThumbnails(new Set());
  }, []);

  useEffect(() => {
    return () => {
      // Limpiar cache al desmontar el componente
      mediaCache.clearCache();
    };
  }, []);

  return {
    getMediaURL,
    getThumbnailURL,
    clearCache,
    cacheSize,
    loadingFiles,
    loadingThumbnails
  };
};
