import { MediaFile } from '../types/chat';

export class MediaCache {
  private cache = new Map<string, string>();
  private thumbnailCache = new Map<string, string>();
  private loadingPromises = new Map<string, Promise<string>>();
  private thumbnailLoadingPromises = new Map<string, Promise<string>>();

  async getMediaURL(fileName: string, files: FileList): Promise<string> {
    // Si ya está en cache, devolverlo
    if (this.cache.has(fileName)) {
      return this.cache.get(fileName)!;
    }

    // Si ya se está cargando, esperar a que termine
    if (this.loadingPromises.has(fileName)) {
      return this.loadingPromises.get(fileName)!;
    }

    // Buscar el archivo en la lista de archivos
    const file = Array.from(files).find(f => f.name === fileName);
    if (!file) {
      throw new Error(`Archivo no encontrado: ${fileName}`);
    }

    // Crear promesa de carga
    const loadingPromise = new Promise<string>((resolve, reject) => {
      try {
        const url = URL.createObjectURL(file);
        this.cache.set(fileName, url);
        resolve(url);
      } catch (error) {
        reject(error);
      }
    });

    this.loadingPromises.set(fileName, loadingPromise);

    try {
      const url = await loadingPromise;
      this.loadingPromises.delete(fileName);
      return url;
    } catch (error) {
      this.loadingPromises.delete(fileName);
      throw error;
    }
  }

  async getThumbnailURL(fileName: string, files: FileList): Promise<string> {
    const thumbnailKey = `thumb_${fileName}`;

    // Si ya está en cache, devolverlo
    if (this.thumbnailCache.has(thumbnailKey)) {
      return this.thumbnailCache.get(thumbnailKey)!;
    }

    // Si ya se está cargando, esperar a que termine
    if (this.thumbnailLoadingPromises.has(thumbnailKey)) {
      return this.thumbnailLoadingPromises.get(thumbnailKey)!;
    }

    // Buscar el archivo en la lista de archivos
    const file = Array.from(files).find(f => f.name === fileName);
    if (!file) {
      throw new Error(`Archivo no encontrado: ${fileName}`);
    }

    // Crear promesa de carga del thumbnail
    const loadingPromise = new Promise<string>(async (resolve, reject) => {
      try {
        let thumbnailUrl: string;

        if (file.type.startsWith('image/')) {
          thumbnailUrl = await generateImageThumbnail(file);
        } else if (file.type.startsWith('video/')) {
          thumbnailUrl = await generateVideoThumbnail(file);
        } else {
          // Para otros tipos, usar el archivo original
          thumbnailUrl = URL.createObjectURL(file);
        }

        this.thumbnailCache.set(thumbnailKey, thumbnailUrl);
        resolve(thumbnailUrl);
      } catch (error) {
        reject(error);
      }
    });

    this.thumbnailLoadingPromises.set(thumbnailKey, loadingPromise);

    try {
      const url = await loadingPromise;
      this.thumbnailLoadingPromises.delete(thumbnailKey);
      return url;
    } catch (error) {
      this.thumbnailLoadingPromises.delete(thumbnailKey);
      throw error;
    }
  }

  clearCache(): void {
    // Liberar URLs de objeto para evitar memory leaks
    this.cache.forEach(url => URL.revokeObjectURL(url));
    this.thumbnailCache.forEach(url => URL.revokeObjectURL(url));
    this.cache.clear();
    this.thumbnailCache.clear();
    this.loadingPromises.clear();
    this.thumbnailLoadingPromises.clear();
  }

  getCacheSize(): number {
    return this.cache.size + this.thumbnailCache.size;
  }
}

export const mediaCache = new MediaCache();

export const generateImageThumbnail = (file: File, maxWidth: number = 150, quality: number = 0.3): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('El archivo no es una imagen'));
      return;
    }

    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('No se pudo crear el contexto del canvas'));
        return;
      }

      // Calcular dimensiones manteniendo aspect ratio
      const aspectRatio = img.height / img.width;
      const thumbnailWidth = Math.min(maxWidth, img.width);
      const thumbnailHeight = thumbnailWidth * aspectRatio;

      canvas.width = thumbnailWidth;
      canvas.height = thumbnailHeight;

      // Dibujar imagen redimensionada
      ctx.drawImage(img, 0, 0, thumbnailWidth, thumbnailHeight);

      // Convertir a blob con calidad reducida
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const thumbnailUrl = URL.createObjectURL(blob);
            resolve(thumbnailUrl);
          } else {
            reject(new Error('Error generando thumbnail'));
          }
        },
        'image/jpeg',
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('Error cargando la imagen'));
    };

    img.src = URL.createObjectURL(file);
  });
};

export const generateVideoThumbnail = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('video/')) {
      reject(new Error('El archivo no es un video'));
      return;
    }

    const video = document.createElement('video');
    video.preload = 'metadata';
    video.onloadedmetadata = () => {
      video.currentTime = 1; // Capturar frame en segundo 1
    };
    video.onseeked = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('No se pudo crear el contexto del canvas'));
        return;
      }

      // Redimensionar para thumbnail
      const maxWidth = 200;
      const aspectRatio = video.videoHeight / video.videoWidth;
      const thumbnailWidth = Math.min(maxWidth, video.videoWidth);
      const thumbnailHeight = thumbnailWidth * aspectRatio;

      canvas.width = thumbnailWidth;
      canvas.height = thumbnailHeight;

      ctx.drawImage(video, 0, 0, thumbnailWidth, thumbnailHeight);

      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          resolve(url);
        } else {
          reject(new Error('Error generando thumbnail de video'));
        }
      }, 'image/jpeg', 0.4);
    };

    video.onerror = () => {
      reject(new Error('Error cargando el video'));
    };

    video.src = URL.createObjectURL(file);
  });
};
