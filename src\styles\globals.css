@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom range input styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #d1d5db;
  height: 4px;
  border-radius: 2px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #25D366;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-track {
  background: #d1d5db;
  height: 4px;
  border-radius: 2px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: #25D366;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Animation for loading */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Animation for date navigation highlight */
@keyframes highlight-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(251, 191, 36, 0.3);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
  }
}

.highlight-message {
  animation: highlight-glow 1.5s ease-out;
}

/* WhatsApp-like message bubbles */
.message-bubble {
  position: relative;
}

.message-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  width: 0;
  height: 0;
}

.message-bubble.own::before {
  right: -8px;
  border-left: 8px solid #25D366;
  border-top: 8px solid transparent;
}

.message-bubble.other::before {
  left: -8px;
  border-right: 8px solid white;
  border-top: 8px solid transparent;
}

/* Custom file input styling */
.file-input-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.file-input-wrapper input[type=file] {
  position: absolute;
  left: -9999px;
}

/* Modal backdrop blur */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Video controls fade */
.video-controls {
  transition: opacity 0.3s ease-in-out;
}

/* Chat background pattern (optional) */
.chat-background {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 50;
    transition: left 0.3s ease-in-out;
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .chat-container {
    width: 100%;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .message-bubble {
    break-inside: avoid;
  }
}
